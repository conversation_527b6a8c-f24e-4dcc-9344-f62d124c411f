import Link from "next/link";

import { AuthStatus } from "@/components/auth-status";

export default function AuthTestPage() {
  return (
    <div className="container mx-auto max-w-4xl p-8">
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">认证功能测试页面</h1>
          <p className="text-muted-foreground mt-2">测试注册、登录和认证状态</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">测试链接</h2>
            <div className="space-y-2">
              <Link 
                href="/auth/v1/register" 
                className="block p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                📝 注册页面 (v1)
              </Link>
              <Link 
                href="/auth/v1/login" 
                className="block p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                🔑 登录页面 (v1)
              </Link>
              <Link 
                href="/dashboard" 
                className="block p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                🏠 仪表板 (需要认证)
              </Link>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">当前认证状态</h2>
            <AuthStatus />
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">测试步骤</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>点击"注册页面"链接，填写注册信息并提交</li>
            <li>注册成功后应该自动登录并跳转到仪表板</li>
            <li>检查右侧的认证状态是否显示已登录</li>
            <li>尝试直接访问仪表板，应该可以正常访问</li>
            <li>点击登出按钮，然后尝试访问仪表板，应该被重定向到登录页</li>
            <li>使用注册的账号在登录页面登录，验证登录功能</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
