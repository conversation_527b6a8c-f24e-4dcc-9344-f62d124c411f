"use client";

import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getCurrentUser, getAccessToken, isAuthenticated, logout, type User } from "@/lib/auth";

export function AuthStatus() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const updateAuthStatus = () => {
      setUser(getCurrentUser());
      setToken(getAccessToken());
      setAuthenticated(isAuthenticated());
    };

    updateAuthStatus();
    
    // 监听storage变化
    const handleStorageChange = () => {
      updateAuthStatus();
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  const handleLogout = () => {
    logout();
    setUser(null);
    setToken(null);
    setAuthenticated(false);
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>认证状态</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <strong>认证状态:</strong> {authenticated ? "已登录" : "未登录"}
        </div>
        
        {user && (
          <div className="space-y-2">
            <div><strong>用户ID:</strong> {user.userId}</div>
            <div><strong>用户名:</strong> {user.username}</div>
            <div><strong>邮箱:</strong> {user.email}</div>
            <div><strong>角色:</strong> {user.role}</div>
          </div>
        )}
        
        {token && (
          <div>
            <strong>Token:</strong> {token.substring(0, 20)}...
          </div>
        )}
        
        {authenticated && (
          <Button onClick={handleLogout} variant="outline" className="w-full">
            登出
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
