# Application Configuration
NODE_ENV=development
PORT=1
APP_NAME=trade-manage-backend

# AWS Configuration
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# AWS S3 Configuration
S3_BUCKET_NAME=trade-manage-files-dev
S3_REGION=ap-southeast-1

# AWS Cognito Configuration
COGNITO_USER_POOL_ID=ap-southeast-1_xxxxxxxxx
COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
COGNITO_REGION=ap-southeast-1

# AWS DynamoDB Configuration
DYNAMODB_REGION=ap-southeast-1
DYNAMODB_TABLE_PREFIX=trade-manage-dev

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# Database Configuration
DB_TABLE_USERS=users
DB_TABLE_TRADES=trades
DB_TABLE_FILES=files

# API Configuration
API_PREFIX=api/v1
SWAGGER_TITLE=Trade Management API
SWAGGER_DESCRIPTION=API for Trade Management System
SWAGGER_VERSION=1.0.0
