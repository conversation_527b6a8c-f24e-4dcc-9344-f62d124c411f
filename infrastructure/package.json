{"name": "trade-manage-infrastructure", "version": "1.0.0", "description": "AWS CDK Infrastructure for Trade Management Backend", "main": "lib/app.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "npm run build && cdk deploy", "destroy": "cdk destroy", "diff": "cdk diff", "synth": "cdk synth"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.10.6", "aws-cdk": "^2.1020.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "dependencies": {"aws-cdk-lib": "^2.115.0", "constructs": "^10.3.0"}}